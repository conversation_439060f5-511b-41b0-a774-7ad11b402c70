# Alerts/ReprocessCases.py
"""
Centralized case reprocessing functionality.
This module provides a unified interface for reprocessing cases across different parts of the application.
"""

import time
import os
import pandas as pd
import asyncio
from typing import Dict, Any, Optional, List, Union
from logdata import log_message
from DatabaseManagement.ImportExport import get_table_from_GZ
import json
import langfuse
from langfuse import observe
import getpass



async def reprocess_cases(
    cases_to_reprocess: Union[pd.DataFrame, List[int], int],
    processing_options: Optional[Dict[str, Any]] = None,
    trace_name: str = "Case Reprocess",
    full_cases_df: Optional[pd.DataFrame] = None,
    plaintiff_df: Optional[pd.DataFrame] = None
) -> bool:
    """
    Unified function for reprocessing cases using the new approach.

    Args:
        cases_to_reprocess: Can be:
            - DataFrame containing cases to reprocess
            - List of case IDs
            - Single case ID (int)
        processing_options: Dictionary of processing options. If None, uses default options.
        trace_name: Name for the trace (for logging/monitoring)
        full_cases_df: Full cases DataFrame. If None, will be loaded from database.
        plaintiff_df: Plaintiff DataFrame. If None, will be loaded from database.

    Returns:
        bool: True if all cases processed successfully, False otherwise
    """

    # Import required modules
    from Alerts.LexisNexis import Login
    from Alerts.LexisNexis.Arrive_On_Case_Page import arrive_on_case_page
    from Alerts.CaseProcessor import process_case

    # Default processing options
    if processing_options is None:
        processing_options = {
            'update_steps': True,
            'process_pictures': True,
            'upload_files_nas': True,
            'upload_files_cos': True,
            'run_plaintiff_overview': True,
            'run_summary_translation': True,
            'run_step_translation': True,
            'save_to_db': True,
            'processing_mode': 'full_reprocess',
            'refresh_days_threshold': 15
        }

    # Convert input to DataFrame of cases to reprocess
    if isinstance(cases_to_reprocess, int):
        # Single case ID
        case_ids = [cases_to_reprocess]
    elif isinstance(cases_to_reprocess, list):
        # List of case IDs
        case_ids = cases_to_reprocess
    elif isinstance(cases_to_reprocess, pd.DataFrame):
        # DataFrame - extract case IDs
        if 'id' in cases_to_reprocess.columns:
            case_ids = cases_to_reprocess['id'].tolist()
        else:
            log_message("DataFrame must contain 'id' column", level="ERROR")
            return False
    else:
        log_message(f"Invalid cases_to_reprocess type: {type(cases_to_reprocess)}", level="ERROR")
        return False

    if not case_ids:
        log_message("No cases to reprocess", level="INFO")
        return True

    # Load DataFrames if not provided
    if full_cases_df is None:
        log_message("Loading cases DataFrame from database...", level="INFO")
        full_cases_df = get_table_from_GZ("tb_case", force_refresh=True)
        if full_cases_df is None or full_cases_df.empty:
            log_message("Failed to load cases DataFrame", level="ERROR")
            return False

    if plaintiff_df is None:
        log_message("Loading plaintiff DataFrame from database...", level="INFO")
        plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=True)
        if plaintiff_df is None or plaintiff_df.empty:
            log_message("Failed to load plaintiff DataFrame", level="ERROR")
            return False

    # Filter cases_df to only include cases we want to reprocess
    cases_to_reprocess_df = full_cases_df[full_cases_df['id'].isin(case_ids)].sort_values('date_filed', ascending=False)

    if cases_to_reprocess_df.empty:
        log_message(f"No matching cases found in database for IDs: {case_ids}", level="WARNING")
        return False

    log_message(f"Starting reprocessing of {len(cases_to_reprocess_df)} cases with {trace_name}", level="INFO")

    success_count = 0
    total_count = len(cases_to_reprocess_df)

    try:
        # Get logged-in browser
        log_message("Getting logged-in browser...", level="INFO")
        driver = Login.get_logged_in_browser()

        if not driver:
            raise Exception("Failed to get logged-in browser")

        # Process each case
        cases_no_ip_then_some_ip = []
        cases_no_ip_then_all_ip = []
        cases_some_ip_then_more_ip = []
        cases_some_ip_then_all_ip = []
        cases_ip_regressed = []
        cases_already_all_ip = []

        for i, (idx, case_row) in enumerate(cases_to_reprocess_df.iterrows()):
            case_id = case_row['id']

            try:
                log_message(f"{i}/{len(cases_to_reprocess_df)} : Reprocessing case {case_id} ({case_row.get('docket', 'Unknown docket')})", level="INFO")

                trace_id = langfuse.get_client().create_trace_id()
                # We create a span just for the purpose of updating the trace. Apparently there is no other way.
                with langfuse.get_client().start_as_current_span(name="Case Information", trace_context={"trace_id": trace_id}) as span:
                    langfuse.get_client().update_current_trace(name=trace_name, user_id=getpass.getuser(), input={"CaseIdentifier": {'case_id': case_id}, "OptionsSummary": processing_options})
                    langfuse.get_client().update_current_span(input={"DocketNumber": case_row['docket'], "CaseID": case_id, "CaseTitle": case_row['title'], "DateFiled": case_row['date_filed'], "PlaintiffID": case_row['plaintiff_id']})

                # Arrive on case page
                arrival_success, arrival_df_slice, case_idx = arrive_on_case_page(driver, full_cases_df, case_identifier={'case_id': case_id}, langfuse_trace_id=trace_id)

                if not arrival_success:
                    log_message(f"Failed to arrive on case page for case {case_id}", level="WARNING")
                    continue
                    
                
                
                # Pre-process state
                pre_ip_state = (full_cases_df.at[case_idx, 'images_status'] or {}).get('ip_manager_state', {})
                pre_ip_json = json.loads(json.dumps(pre_ip_state))

                # Process the case
                success, updated_case_df_row = await process_case(driver, processing_options, full_cases_df, case_idx, plaintiff_df, langfuse_trace_id=trace_id)

                # Post-process state
                post_ip_state = (updated_case_df_row.at[case_idx, 'images_status'] or {}).get('ip_manager_state', {})
                post_ip_json = json.loads(json.dumps(post_ip_state))

                for ip_type in post_ip_json:
                    pre_raw = pre_ip_json.get(ip_type, {})
                    post_raw = post_ip_json.get(ip_type, {})

                    pre_state = {
                        "goal_met": pre_raw.get("goal_met", False),
                        "count": pre_raw.get("count", 0)
                    }
                    post_state = {
                        "goal_met": post_raw.get("goal_met", False),
                        "count": post_raw.get("count", 0)
                    }

                    if pre_state['goal_met'] and not post_state['goal_met']:
                        cases_ip_regressed.append((case_id, ip_type))

                    elif pre_state['goal_met'] and post_state['goal_met']:
                        cases_already_all_ip.append((case_id, ip_type))

                    elif not pre_state['goal_met'] and not post_state['goal_met']:
                        if pre_state['count'] == 0 and post_state['count'] > 0:
                            cases_no_ip_then_some_ip.append((case_id, ip_type))
                        elif pre_state['count'] > 0 and post_state['count'] > pre_state['count']:
                            cases_some_ip_then_more_ip.append((case_id, ip_type))
                        elif pre_state['count'] > 0 and post_state['count'] < pre_state['count']:
                            cases_ip_regressed.append((case_id, ip_type))

                    elif not pre_state['goal_met'] and post_state['goal_met']:
                        if pre_state['count'] == 0:
                            cases_no_ip_then_all_ip.append((case_id, ip_type))
                        elif pre_state['count'] > 0:
                            cases_some_ip_then_all_ip.append((case_id, ip_type))

                if success:
                    success_count += 1

            except Exception as case_e:
                import traceback
                log_message(f"Error reprocessing case {case_id}: {str(case_e)}, {traceback.format_exc()}", level="ERROR")
                continue
        
        # Create tracking dictionary with all variables
        tracking_dict = {
            'cases_no_ip_then_some_ip': cases_no_ip_then_some_ip,
            'cases_no_ip_then_all_ip': cases_no_ip_then_all_ip,
            'cases_some_ip_then_more_ip': cases_some_ip_then_more_ip,
            'cases_some_ip_then_all_ip': cases_some_ip_then_all_ip,
            'cases_ip_regressed': cases_ip_regressed,
            'cases_already_all_ip': cases_already_all_ip,
            'total_count': total_count
        }

        # Close the browser
        driver.quit()
        log_message(f"🔥🔥🔥 Completed reprocessing: {success_count}/{total_count} cases successful 🔥🔥🔥", level="INFO")

        return success_count == total_count, tracking_dict

    except Exception as e:
        log_message(f"Critical error during case reprocessing: {str(e)}", level="ERROR")
        import traceback
        log_message(traceback.format_exc(), level="ERROR")
        return False


def _get_nested_value(data: Any, keys: List[str], default: Any = None) -> Any:
    """
    Safely retrieves a value from a nested dictionary or object using a list of keys.
    Returns the default value if any key in the path is not found or if data is not a dictionary.
    """
    if not isinstance(data, dict):
        return default
    
    current_value = data
    for key in keys:
        if isinstance(current_value, dict) and key in current_value:
            current_value = current_value[key]
        else:
            return default
    return current_value

def _has_valid_copyright_image(copyrights_dict: dict) -> bool:
    """
    Checks if a given 'copyrights' dictionary contains at least one valid copyright image.
    A valid image is one where the key (image filename) corresponds to its 'full_filename' value
    by appending '_full.webp' to the key's base name.
    """
    if not copyrights_dict:
        return False
    
    for key, value_dict in copyrights_dict.items():
        if isinstance(value_dict, dict) and "full_filename" in value_dict and \
           isinstance(value_dict["full_filename"], list) and \
           len(value_dict["full_filename"]) > 0:
            
            if value_dict["full_filename"][0].replace("_full.webp", ".webp") != key or "TinEye" in key or "Google" in key or "GenAI" in key:
                return True # Found at least one valid copyright image
    return False # No valid copyright images found in the dictionary

def filter_plaintiffs_with_no_copyright_images(cases_df: pd.DataFrame) -> list:
    """
    Filters and returns a list of plaintiff_ids for whom ALL associated cases
    have no valid copyright images.
    
    Args:
        cases_df (pd.DataFrame): DataFrame containing case data, expected to have
                                 'plaintiff_id' and 'images' columns.
                                 The 'images' column should contain dictionaries
                                 with a 'copyrights' key, which is itself a dictionary.
                                 
    Returns:
        list: A list of plaintiff_ids that meet the criteria.
    """
    
    # Apply the check for each case's 'copyrights' dictionary
    cases_df['has_valid_copyright_image'] = cases_df['images'].apply(
        lambda img_col: _has_valid_copyright_image(img_col.get('copyrights', {}))
        if isinstance(img_col, dict) else False
    )
    
    # Group by plaintiff_id and check if ALL cases for that plaintiff have NO valid copyright images
    plaintiffs_no_copyright_images = cases_df.groupby('plaintiff_id')['has_valid_copyright_image'].apply(lambda group: not group.any())
    
    # Get the plaintiff_ids where the condition is True
    filtered_plaintiff_ids = plaintiffs_no_copyright_images[plaintiffs_no_copyright_images].index.tolist()
    
    # Drop the temporary column
    cases_df.drop(columns=['has_valid_copyright_image'], inplace=True)
    
    return filtered_plaintiff_ids





# Example Usage (Illustrative - requires actual instances)
async def main():
    start_time = time.time()
    # This block is for testing/illustration purposes only
    # Replace with actual setup in your application context
    print("CaseProcessor module loaded. Define actual instances and call process_case.")
    # Example:
    cases_df = get_table_from_GZ("tb_case", force_refresh=True) # Added force_refresh=False
    plaintiffs_df = get_table_from_GZ("tb_plaintiff", force_refresh=True) # Added force_refresh=False

    my_df = cases_df[pd.isna(cases_df['plaintiff_id'])] # Cases without plaintiff_id

    # Normalize the 'images' column
    default_empty_images = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
    cases_df['images'] = cases_df['images'].apply(lambda x: default_empty_images if pd.isna(x) or x == {} else x)

    # Identify plaintiffs where NONE of their cases have IP images
    plaintiffs_with_no_images = cases_df.groupby('plaintiff_id')['images'].apply(lambda group: all(img == default_empty_images for img in group))
    plaintiff_ids_with_no_images = plaintiffs_with_no_images[plaintiffs_with_no_images].index.tolist()

    # Filter cases_df to include only cases from these plaintiffs
    cases_from_plaintiffs_with_no_images_df = cases_df[cases_df['plaintiff_id'].isin(plaintiff_ids_with_no_images)]

    # Now, from the cases_from_plaintiffs_with_no_images_df, pick the most recent one for each plaintiff
    # This ensures we are only considering plaintiffs who have NO images on ANY of their cases.
    representative_cases_no_ip_df = cases_from_plaintiffs_with_no_images_df.loc[cases_from_plaintiffs_with_no_images_df.groupby('plaintiff_id')['date_filed'].idxmax()]
    representative_cases_no_ip_df = representative_cases_no_ip_df[representative_cases_no_ip_df["nos_description"].notna()]

    my_df_with_ai_summary = representative_cases_no_ip_df[representative_cases_no_ip_df["aisummary"].fillna("") != ""]
    my_df_without_ai_summary = representative_cases_no_ip_df[representative_cases_no_ip_df["aisummary"].fillna("") == ""]
    #=> we have 3 priority groups:
    # (1) new cases without plaintiff ID,
    # (2) existing cases (1 per plaintiff) without AI summary,
    # (3) existing cases (1 per plaintiff) with AI summary
    
    
    ### Copyright fixing:
    # 1. Copyright cases with missing IP, 1 per plaintiff, not plaintiff 9
    not_plaintiff_9_df = cases_df[cases_df["plaintiff_id"] != 9]
    not_plaintiff_9_df['nos_description'] = not_plaintiff_9_df['nos_description'].apply(lambda x: "No_NOS" if pd.isna(x) else x)
    
    # for index, row in not_plaintiff_9_df.iterrows():
    #     if row["images_status"] and isinstance(row["images_status"], str):
    #         try:
    #             json.loads(row["images_status"])
    #         except:
    #             print(f"images_status is not valid json: {row['docket']}, {row['images_status']}")
    not_plaintiff_9_df["images_status"] = not_plaintiff_9_df["images_status"].apply(lambda x: json.loads(x) if not pd.isna(x) and isinstance(x, str) else x)
    copyright_nos_df = not_plaintiff_9_df[not_plaintiff_9_df["nos_description"].str.contains("Copyrights")]
    
    # Safely access 'is_relevant' using the new helper function
    not_plaintiff_9_df['is_relevant_copyright'] = not_plaintiff_9_df.apply(
        lambda row: _get_nested_value(row.get('images_status'), ['ip_manager_state', 'copyright', 'is_relevant'], False),
        axis=1
    )
    copyright_relevant = not_plaintiff_9_df[not_plaintiff_9_df['is_relevant_copyright'] == True]
    
    all_copyright_df = pd.concat([copyright_nos_df, copyright_relevant]).drop_duplicates(subset=['docket', 'date_filed'])

    filtered_plaintiff_ids = filter_plaintiffs_with_no_copyright_images(all_copyright_df)
    cases_from_plaintiffs_with_no_copyright_images_df = all_copyright_df[all_copyright_df['plaintiff_id'].isin(filtered_plaintiff_ids)]
    representative_cases_no_copyright_df = cases_from_plaintiffs_with_no_copyright_images_df.loc[cases_from_plaintiffs_with_no_copyright_images_df.groupby('plaintiff_id')['date_filed'].idxmax()]
    import datetime
    representative_cases_no_copyright_df = representative_cases_no_copyright_df[representative_cases_no_copyright_df["update_time"] < datetime.datetime(2025, 6, 8, 1, 43, 0)]
    
    
    processing_options = {
        'update_steps': True, # Forces update of case steps from source
        'process_pictures': True, # Process images from PDFs, always true for now!
        'upload_files_nas': True, # Upload files to NAS
        'upload_files_cos': True, # Upload files to COS
        'run_plaintiff_overview': True, # Run AI plaintiff overview
        'run_summary_translation': True, # Run AI summary translation
        'run_step_translation': True, # Run AI step translation
        'save_to_db': True, # Save results to DB
        'processing_mode': 'full_reprocess', # Processing mode: 'full_reprocess' (default) or 'resume'
        'refresh_days_threshold': 2000 # Refresh threshold in days
    }
    
    urgent = cases_df[(cases_df["date_filed"]>=pd.to_datetime("2025-06-05").date())]
    #sort
    urgent = urgent.sort_values(by='date_filed', ascending=False)
    
    # os.environ["LNusername"] = "jslawpclegal"
    # os.environ["LNpassword"] = "Khlvshi2021="
    # os.environ["LN_chrome_folder"] = "chrome_user_data_pclegal"
    
    await reprocess_cases(cases_to_reprocess=my_df, processing_options=processing_options, trace_name= "Manual Case Processor", full_cases_df=cases_df, plaintiff_df=plaintiffs_df)
    
    # Cases where plaintiff_id is not 9 and nos_description is na
    remaining_cases = cases_df[(cases_df["plaintiff_id"] != 9) & (cases_df["nos_description"].isna())]
    # Now lets select only 1 per plaintiff_id, the most recent one
    remaining_cases = remaining_cases.loc[remaining_cases.groupby('plaintiff_id')['date_filed'].idxmax()]
    await reprocess_cases(cases_to_reprocess=remaining_cases, processing_options=processing_options, trace_name= "Manual Case Processor", full_cases_df=cases_df, plaintiff_df=plaintiffs_df)
    
    # case_id_to_process = 14196
    # await reprocess_cases(cases_to_reprocess=case_id_to_process, processing_options=processing_options, trace_name= "Manual Case Processor", full_cases_df=cases_df, plaintiff_df=plaintiffs_df)

if __name__ == '__main__':
    asyncio.run(main())
