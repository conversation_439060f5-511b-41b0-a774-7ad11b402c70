from celery import Celery
import os
from celery.signals import worker_init
from Check.RAG.RAG_Inference import load_all_models
from Check.Data_Cache import update_dataframe_cache

# It's a good practice to use environment variables for configuration
REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')

@worker_init.connect
def init_worker(**kwargs):
    os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
    os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-bac4b191-590d-4059-89d6-e5ff73e91b48"
    print("Set Langfuse environment variables for worker.")
    """
    Load models and cache in the main worker process before it forks.
    This allows child processes to share the memory via copy-on-write.
    """
    print("Initializing models for master process...")
    load_all_models()
    update_dataframe_cache()
    print("Models initialized and shared with workers.")

# Create the Celery app instance
# The first argument is the name of the current module, which is 'Check.Celery.celery_app'
# The broker is where Celery sends task messages.
# The backend is where Celery stores task states and results.
celery = Celery(
    'tasks',
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=['Check.Celery.tasks']
)

# Optional Celery configuration
celery.conf.update(
    task_serializer='json',
    accept_content=['json'],  # Ignore other content
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    # Lower the prefetch multiplier to prevent a single worker from grabbing too many tasks at once
    # This is useful for long-running tasks.
    worker_prefetch_multiplier=1,
    # Acknowledge tasks after they have been executed, not before.
    # This ensures that if a worker crashes, the task will be re-queued.
    task_acks_late=True,
)

if __name__ == '__main__':
    celery.start()