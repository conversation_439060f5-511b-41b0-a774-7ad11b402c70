import time
import random
import json # Added for URL building
import os
import urllib.parse
import undetected_chromedriver as uc
import pandas as pd
import re
from typing import List

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import StaleElementReferenceException, TimeoutException, NoSuchElementException
from typing import Optional

from Alerts.Chrome_Driver import get_driver, move_mouse_to, random_delay, driver_is_alive
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.LLM_shared import get_json
from typing import Any
from logdata import log_message
import traceback
import asyncio


# ❌⚠️📥🔥✅

USCO_BASE_URL = "https://publicrecords.copyright.gov"


def close_feedback_modal(driver):
    """
    Checks if the feedback modal is present and, if so, clicks the close button.
    """
    try:
        modal_close_button = WebDriverWait(driver, 3).until(
            EC.element_to_be_clickable((
                By.XPATH,
                "//div[contains(@class, 'modal-content')]//button[contains(@class, 'close-modal')]"
            ))
        )
        move_mouse_to(driver, modal_close_button)
        modal_close_button.click()
        print("⚠️ Feedback modal closed.")
        time.sleep(1)
        return True
    except Exception:
        # Modal not present; do nothing.
        return False

def parse_record_details(driver):
    """
    Scrapes details from a detailed record page.
    Returns a dictionary mapping field names to their extracted values.
    It looks for the <cd-record> element and then:
      - For each <li> element inside, it grabs the label from the <strong> tag.
      - If an inner <ul> exists, it concatenates the child <li> texts.
      - Otherwise, it removes the label text from the overall text.
    """

    details_retries = 0
    while details_retries < 3:
        details = {}
        try:
            cd_record = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "cd-record"))
            )
            # Explicitly wait for the first li element to be present
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//cd-record//li"))
            )
            li_elements = cd_record.find_elements(By.TAG_NAME, "li")
            for li in li_elements:
                label_elems = li.find_elements(By.TAG_NAME, "strong")
                if len(label_elems) == 0:
                    continue  # if not label on that row, continue to the next line
                label_elem = label_elems[0]

                label = label_elem.text.strip().rstrip(":")

                # Try to see if there is a <ul> inside (for multi-value fields)
                ul_elems = li.find_elements(By.TAG_NAME, "ul")
                if len(ul_elems) == 0: # Single value field
                    # Remove the label text from the full text
                    full_text = li.text.strip()
                    value = full_text.replace(label_elem.text, "").strip()
                else: # Multi-value field
                    li_texts = [item.text.strip() for item in ul_elems if item.text.strip()]
                    value = ", ".join(li_texts)
                details[label] = value
            break
        except Exception as e:
            found = close_feedback_modal(driver)

            if 'cd_record' in locals() and cd_record.text == "":
                driver.get(driver.current_url)
                time.sleep(1)
                cd_record = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "cd-record"))
                )
                if cd_record.text == "":
                    print(f"USCO parse_record_details: this URL does not load: {driver.current_url}")
                    return "Wrong URL"


            print(f"Error processing details page, pop up was found: {found}. Exception is: ", e)

            details_retries += 1
            if details_retries == 3:
                print(f"Failed to process detail page after 3 retries.")
            else:
                print(f"Retrying detail page (Attempt {details_retries} of 3)...")

                # Ensure we are on the correct tab by checking the URL or title
                if "publicrecords.copyright.gov/detailed-record" in driver.current_url:
                    print("We are on the correct tab.")
                else:
                    print("We are not on the correct tab.")
                    return "Wrong Tab"

                time.sleep(2)  # Wait for 2 seconds before retrying
    return details

async def format_record(details): # Added reg_no_standardized
    """
    Maps the scraped details to the target structure.
    Uses standardized column names.
    If any field is missing, an empty string is set.
    """
    
    registration_number = details.get("Registration Number / Date", "").split("/", 1)[0].strip()
    registration_number = await extract_formated_copyright_registration_number(registration_number)
    parsed_date_str = details.get("Registration Number / Date", "").split("/", 1)[1].strip()

    return {
        "registration_number": registration_number, # Standardized registration number
        "registration_date": parsed_date_str, # Parsed date as YYYY-MM-DD string or None
        "type_of_work": details.get("Type of Work", "").strip(),
        "title": details.get("Title", "").strip(),
        "application_title": details.get("Application Title", "").strip(),
        "date_of_creation": details.get("Date of Creation", "").strip(),
        "date_of_publication": details.get("Date of Publication", "").strip(),
        "copyright_claimant": details.get("Copyright Claimant", "").strip(),
        "authorship_on_application": details.get("Authorship on Application", "").strip(),
        "rights_and_permissions": details.get("Rights and Permissions", "").strip(),
        "description": details.get("Description", "").strip(),
        "nation_of_first_publication": details.get("Nation of First Publication", "").strip(),
        "basis_of_claim": details.get("Basis of Claim", "").strip(),
        "names": details.get("Names", "").strip()
    }

def extract_total_results(driver) -> int:
    """
    Extracts the total number of search results from the search results page.
    Looks for text like "Displaying: 1-X of Y" or "Top search results: Y".
    """
    try:
        # Wait for the element containing the total count
        # Look for the span within cd-paging-display or the div with "Top search results"
        total_results_element = WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((
                By.XPATH,
                "//cd-paging-display/span | //div[@class='caption']//div[contains(text(), 'Top search results:')] | //div[contains(@class, 'caption') and contains(text(), 'Top search results:')] "
            ))
        )
        text = total_results_element.text
        # Use regex to find the number after "of " or after "Top search results: "
        match = re.search(r"of (\d+)|Top search results:\s*([\d,]+)", text)
        if match:
            # Group 1 is from "of (\d+)", Group 2 is from "Top search results:\s*([\d,]+)"
            # We need to handle commas in the number
            number_str = match.group(1) if match.group(1) else match.group(2)
            return int(number_str.replace(",", ""))
        else:
            print(f"Could not extract total results from text: {text}")
            return 0
    except Exception as e:
        print(f"Error extracting total results: {e}")
        return 0

def extract_claimants_from_page(driver) -> set:
    """
    Extracts unique claimant names from the current search results page.
    """
    claimants = set()
    try:
        # Wait for the first row to be present
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex') and not(contains(@class, 'footer'))]"))
        )
        # Fetch the rows on the current page
        rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex') and not(contains(@class, 'footer'))]")

        for row in rows:
            try:
                # Find the element containing the Claimant label and then the value
                claimant_elem = row.find_element(By.XPATH, ".//li[.//strong[text()='Claimant:']]//div[@class='control-value']")
                claimant_text = claimant_elem.text.strip()
                if claimant_text:
                    # Add the first claimant if there are multiple separated by commas
                    claimants.add(claimant_text.split(',')[0].strip())
            except NoSuchElementException:
                # Claimant field not found for this row, skip
                continue
            except Exception as e:
                print(f"Error extracting claimant from row: {e}")
                continue
    except Exception as e:
        print(f"Error fetching rows to extract claimants: {e}")

    return claimants


def _build_usco_search_url(
    query_details: dict, # {"name": str, "column_name": str, "type_of_query": str}
    page_number: int = 1,
    records_per_page: int = 50,
    date_params_str: str = "", # e.g., "&date_field=...&start_date=...&end_date=..." or specific model params
    sort_field: Optional[str] = None,
    sort_order: Optional[str] = "asc",
    type_of_record: Optional[List[str]] = None,
    registration_status: Optional[List[str]] = None,
    registration_class: Optional[List[str]] = None, # e.g. ["VA"]
    highlight: bool = True
) -> str:
    """Helper function to construct an advanced search URL for USCO."""
    parent_query_list = [{
        "operator_type": "AND",
        "column_name": query_details["column_name"],
        "type_of_query": query_details["type_of_query"],
        "query": query_details["name"]
    }]
    parent_query_encoded = urllib.parse.quote(json.dumps(parent_query_list))

    url = f"{USCO_BASE_URL}/advanced-search?page_number={page_number}"
    url += f"&parent_query={parent_query_encoded}"
    url += f"&records_per_page={records_per_page}"

    if type_of_record:
        url += f"&type_of_record={urllib.parse.quote(json.dumps(type_of_record))}"
    if sort_field:
        url += f"&sort_field=%22{sort_field}%22"
    if sort_order:
        url += f"&sort_order=%22{sort_order}%22"
    if highlight:
        url += f"&highlight=true"
    if registration_status:
        url += f"&registration_status={urllib.parse.quote(json.dumps(registration_status))}"
    if registration_class:
        url += f"&registration_class={urllib.parse.quote(json.dumps(registration_class))}"

    url += f"&model=%22%22{date_params_str}" # date_params_str contains specific date filters or is empty
    return url

def evaluate_search_combination(driver, name: str, column_name: str, type_of_query: str, date_range_model_param: str, record_per_page: int) -> dict:
    """
    Performs a preliminary search with the given parameters, extracts total results
    and unique claimants from the first page, and calculates a score.
    Returns a dictionary with evaluation results and score.
    """
    log_message(f"Evaluating search: Name='{name}', Column='{column_name}', Type='{type_of_query}'", level="DEBUG")
    query_details = {"name": name, "column_name": column_name, "type_of_query": type_of_query}
    search_url = _build_usco_search_url(
        query_details=query_details,
        page_number=1,
        records_per_page=record_per_page,
        date_params_str=date_range_model_param, # This is the string like "&year_range_from=..."
        sort_field="representative_date",
        sort_order="asc",
        type_of_record=["registration"],
        registration_status=["published"],
        registration_class=["VA"], # Common default for visual arts
        highlight=True
    )

    try:
        driver.get(search_url)

        # Check for "No search results" message
        try:
            WebDriverWait(driver, 5).until(EC.text_to_be_present_in_element((By.XPATH, "//body"), "No search results were found"))
            log_message(f"No search results found for combination: Name='{name}', Column='{column_name}', Type='{type_of_query}'", level="DEBUG")
            return {"name": name, "column_name": column_name, "type_of_query": type_of_query, "total_results": 0, "unique_claimants": 0, "score": 0}
        except TimeoutException:
            # No "no results" message, proceed to extract details
            pass

        total_results = extract_total_results(driver)
        unique_claimants = extract_claimants_from_page(driver)
        unique_claimants_count = len(unique_claimants)

        # Calculate score
        score = 0
        if total_results > 0:
            score += 10 - min(9, total_results // 200) # Max 10 points for results (10 for 1-199, 1 for 1800+, 0 for 2000+)
        if type_of_query == "exact":
            score += 10
        if unique_claimants_count == 1:
            score += 5

        log_message(f"Evaluation result: Total={total_results}, Claimants={unique_claimants_count}, Score={score}", level="DEBUG")
        return {"name": name, "column_name": column_name, "type_of_query": type_of_query, "total_results": total_results, "unique_claimants": unique_claimants_count, "score": score, "url": search_url}

    except Exception as e:
        log_message(f"Error during evaluation for combination Name='{name}', Column='{column_name}', Type='{type_of_query}': {e}", level="ERROR")
        return {"name": name, "column_name": column_name, "type_of_query": type_of_query, "total_results": 0, "unique_claimants": 0, "score": -1, "url": search_url} # Assign a negative score on error

def find_best_search_combination(driver, final_search_terms: List[str], date_range_model_param: str = "", record_per_page: int = 50) -> Optional[dict]:
    """
    Evaluates different search combinations (name, column, type) and returns
    the parameters of the combination with the highest score.
    """
    best_score = -1
    best_params = None
    evaluated_combinations = []

    column_options = ["claimants", "all_names"]
    query_options = ["exact", "phrase"]

    for name in final_search_terms:
        name_had_results = False # Flag to see if this name yielded any results with 'claimants'
        for col_name in column_options:
            if name_had_results and col_name == "all_names":
                log_message(f"Skipping 'all_names' for '{name}' as 'claimants' yielded results.", level="DEBUG")
                continue # Skip 'all_names' if 'claimants' already found results for this name

            col_name_had_results = False # Flag to see if this name/col_name yielded any results with 'exact'
            for query_type in query_options:
                if col_name_had_results and query_type == "phrase":
                     log_message(f"Skipping 'phrase' for '{name}'/'{col_name}' as 'exact' yielded results.", level="DEBUG")
                     continue # Skip 'phrase' if 'exact' already found results for this name/column

                eval_result = evaluate_search_combination(driver, name, col_name, query_type, date_range_model_param, record_per_page)
                evaluated_combinations.append(eval_result)

                if eval_result["total_results"] > 0:
                    if col_name == "claimants":
                         name_had_results = True
                    if query_type == "exact":
                         col_name_had_results = True

                if eval_result["score"] > best_score:
                    best_score = eval_result["score"]
                    best_params = {"name": name, "column_name": col_name, "type_of_query": query_type, "url": eval_result["url"]}

                # Early exit if we find a highly confident match (exact, single claimant, some results)
                if best_score >= 15 and best_params and best_params.get("type_of_query") == "exact" and eval_result.get("unique_claimants") == 1:
                     log_message(f"Found highly confident combination (score {best_score}). Stopping search combination evaluation.", level="INFO")
                     return best_params

    if best_score <= 0:
        log_message("No search combination yielded positive results.", level="INFO")
        return None # No combination found results

    log_message(f"Best search combination found: Name='{best_params['name']}', Column='{best_params['column_name']}', Type='{best_params['type_of_query']}' with score {best_score} from {best_params['url']}", level="INFO")
    return best_params

async def get_info_from_USCO_using_reg_no(reg_no, existing_driver=None):
    """
    Navigates to the detailed record page of the Copyright Public Records site using the given registration number.
    Returns a dictionary containing the scraped details.
    """
    query_details = {"name": reg_no, "column_name": "registration_numbers", "type_of_query": "phrase"}
    search_url = _build_usco_search_url(
        query_details=query_details,
        page_number=1,
        records_per_page=10, # Typically only one result expected
        date_params_str="",  # No specific date range for direct reg_no lookup
        sort_order="asc",    # Default sort, though likely irrelevant for single result
        highlight=True,
        # No type_of_record, registration_status, registration_class needed for direct reg_no lookup
    )

    # print(f"Debug: get_info_from_USCO_using_reg_no search_url: {search_url}")

    # Check if driver is already alive and use it, otherwise get a new one
    if existing_driver is None:
        try:
            driver = get_driver() # Always get a new driver for single reg_no lookup for isolation
        except Exception as e:
            log_message(f"Error getting driver: {e}", level="ERROR")
            return None
    else:
        driver = existing_driver


    try:
        click_attempts = 0
        link_clicked_successfully = False
        while click_attempts < 3 and not link_clicked_successfully:
            driver.get(search_url)
            body_element = WebDriverWait(driver, 5).until(EC.presence_of_element_located((By.XPATH, "//body")))
            if "No search results were found" in body_element.text:
                log_message(f"❌ No results found for registration number: {reg_no}.", level="INFO")
                return None

            try:
                # Wait for the search results table to be present
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "table-list-of-search-results")))
                # Wait for the first row to be present
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex') and not(contains(@class, 'footer'))]")))
                # Wait for the first row to have some text content
                WebDriverWait(driver, 10).until(
                    lambda d: d.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex')  and not(contains(@class, 'footer'))]")[0].text.strip() != ""
                )

                current_rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class,'d-flex') and not(contains(@class, 'footer'))]")
                if not current_rows:
                    log_message(f"❌ No results found for registration number: {reg_no} (rows disappeared).", level="WARNING")
                    return None

                # --- Modified row selection logic ---
                selected_row_element = None
                for row_element_candidate in current_rows:
                    row_text = row_element_candidate.text
                    # Ensure reg_no (which is the function input) is a string for the 'in' check
                    if str(reg_no) in row_text and "Type of Work" in row_text:
                        selected_row_element = row_element_candidate
                        log_message(f"Selected row containing reg_no '{reg_no}' and 'Type of Work'.", level="DEBUG")
                        break
                
                if not selected_row_element:
                    selected_row_element = current_rows[0] # Fallback to the first row
                    log_message(f"Fallback: Selected the first row as no specific match found for reg_no '{reg_no}' and 'Type of Work'.", level="DEBUG")
                # --- End modified row selection logic ---

                a_link_element = selected_row_element.find_element(By.XPATH, ".//a[contains(@class, 'link')]")
                # Scroll into view and click
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", a_link_element)
                time.sleep(0.2) # Brief pause after scroll
                a_link_element.click()
                random_delay(1, 2) # Wait for page to navigate
                link_clicked_successfully = True
            except StaleElementReferenceException:
                click_attempts += 1
                log_message(f"StaleElementReferenceException when trying to click link for {reg_no}. Attempt {click_attempts}/3.", level="WARNING")
                if click_attempts < 3:
                    log_message("Refreshing page to resolve staleness.", level="DEBUG")
                    driver.refresh() 
                    time.sleep(2) # Wait for refresh to complete
                else:
                    log_message(f"Failed to click link for {reg_no} after 3 attempts due to StaleElementReferenceException.", level="ERROR")
                    raise # Re-raise the exception to be caught by the outer try-except
            except (TimeoutException, NoSuchElementException) as e_find_click:
                click_attempts += 1
                log_message(f"Error finding/clicking link for {reg_no} (Attempt {click_attempts}/3): {type(e_find_click).__name__}", level="WARNING")
                if click_attempts < 3:
                    driver.refresh()
                    time.sleep(2)
                else:
                    log_message(f"Failed to find/click link for {reg_no} after 3 attempts.", level="ERROR")
                    raise

        if not link_clicked_successfully:
            log_message(f"Could not click the link for {reg_no} after multiple attempts.", level="ERROR")
            return None

        # Close modal in the detail page if it appears
        close_feedback_modal(driver)

        # Parse record details and map the fields
        details = parse_record_details(driver)
        if not details or not details.get("Registration Number / Date"):
            log_message("Details were empty on first try, waiting and retrying parse_record_details...", level="WARNING")
            time.sleep(1)
            details = parse_record_details(driver)
            if not details:
                log_message("Failed to get details after retry for parse_record_details.", level="ERROR")
                return None

        formatted_record = await format_record(details)
        return formatted_record

    except Exception as e:
        log_message(f"Error retrieving information for reg_no {reg_no} (URL: {search_url}): {str(e)}\n{traceback.format_exc()}", level="ERROR")
        return None
    finally:
        if existing_driver is None:
            driver.quit()

async def scrape_USCO(name: str = None, driver: Optional[uc.Chrome] = None, column_name: Optional[str] = None, type_of_query: Optional[str] = None,
                start_date: Optional[str] = None, end_date: Optional[str] = None, df_existing: Optional[pd.DataFrame] = None, ip_manager: Optional[Any] = None,
                max_results: Optional[int] = None) -> Optional[pd.DataFrame]:
    """
    Performs the full scrape for a *single* specified search combination.
    Navigates to the advanced search page of the Copyright Public Records site with
    custom start_date and end_date (e.g. "2025-01-01 00:00:00"). It loops over every search
    result page, opens each record in a new tab via Control+Click, extracts the required
    fields, closes the tab, and finally assembles all records into a pandas DataFrame.
    Returns the DataFrame of scraped records.
    """
    driver_created_internally = False
    if driver is None:
        log_message("Driver not provided to scrape_USCO, creating a new one.", level="INFO")
        try:
            driver = get_driver()
            driver_created_internally = True
        except Exception as e:
            log_message(f"Failed to create driver in scrape_USCO: {e}", level="ERROR")
            return None

    _column_name = column_name if column_name is not None else "all_names"
    _type_of_query = type_of_query if type_of_query is not None else "phrase"

    date_params_str = ""
    if start_date is not None and end_date is not None:
        start_date_enc = urllib.parse.quote(f'"{start_date}"')
        end_date_enc = urllib.parse.quote(f'"{end_date}"')
        date_params_str = f"&date_field=%22representative_date%22&start_date={start_date_enc}&end_date={end_date_enc}"

    query_details = {
        "name": name, # This is the actual search term
        "column_name": _column_name,
        "type_of_query": _type_of_query
    }

    records = []
    page_number = 1
    record_per_page = 50
    last_page = True # In case we crash before testing if we are on the last page

    while True:
        page_retries = 0
        while page_retries < 3:  # Retry the page up to 3 times
            try:
                # Check if the driver is still "alive" and recreate it if necessary
                if driver_created_internally and not driver_is_alive(driver):
                    log_message("Internally created driver is not alive. Recreating...", level="WARNING")
                    if driver is not None:
                        try:
                            driver.quit()
                        except Exception: pass
                    try:
                        driver = get_driver()
                    except Exception as e_get_driver:
                        log_message(f"Failed to recreate driver: {e_get_driver}", level="ERROR")
                        # Potentially raise or return to prevent infinite loop if get_driver consistently fails
                        raise Exception("Failed to maintain a live driver instance.") from e_get_driver

                search_url = _build_usco_search_url(
                    query_details=query_details,
                    page_number=page_number,
                    records_per_page=record_per_page,
                    date_params_str=date_params_str,
                    sort_field="representative_date",
                    sort_order="asc",
                    type_of_record=["registration"],
                    registration_status=["published"],
                    registration_class=["VA"], # Defaulting to Visual Arts, make parameter if more flexibility needed
                    highlight=True
                )

                driver.get(search_url)

                # Check for "No search results" message first
                no_results_found_on_page = False
                try:
                    # Wait directly for the text "No search results were found" to be present in the body
                    WebDriverWait(driver, 5).until(EC.text_to_be_present_in_element((By.XPATH, "//body"), "No search results were found"))
                    print(f"✅ No search results found for the query on page {page_number}. Ending scrape for this query.")
                    no_results_found_on_page = True
                except Exception as e_body_check: # Catch other potential errors during body check
                    pass
                if no_results_found_on_page:
                    print(f"Found 'No search results'.")
                    last_page = True
                    break  # Break from the page_retries loop, outer loop will then break.

                # If no "no results" message, proceed to wait for the search results table.
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "table-list-of-search-results")))

                # Wait for the first row to be present.
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex')]")))
                # Close any feedback modal if it appears.
                found = close_feedback_modal(driver)

                # Wait for the first row to have some text content before proceeding
                WebDriverWait(driver, 10).until(
                    lambda d: driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex')]")[0].text.strip() != ""
                )

                # Fetch the rows
                rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex')]")

                print(f"Found {len(rows)} result rows on page {page_number}")

                if len(rows) < record_per_page:
                    print(f"Page {page_number} has less than {record_per_page} rows. Assuming it is the last page.")
                    last_page = True
                else:
                    last_page = False

                row_index = 0
                while row_index < len(rows):
                    row_retries = 0
                    row_processed_successfully = False
                    while row_retries < 3 and not row_processed_successfully:
                        try:
                            # Ensure we are on the main search results tab
                            if len(driver.window_handles) > 1:
                                main_window_handle = driver.window_handles[0]
                                for handle_idx in range(len(driver.window_handles) - 1, 0, -1): # Close newest tabs first
                                    driver.switch_to.window(driver.window_handles[handle_idx])
                                    if driver.current_url.startswith(USCO_BASE_URL + "/detailed-record"):
                                        driver.close()
                                driver.switch_to.window(main_window_handle)

                            if row_index >= len(rows):
                                log_message(f"Row index {row_index} out of bounds for current rows (len: {len(rows)}). Breaking row processing.", level="WARNING")
                                break # from row_retries loop

                            current_row_element = rows[row_index]
                            a_link = current_row_element.find_element(By.XPATH, ".//a[contains(@class, 'link')]")

                            # Check if record already exists in df_existing
                            reg_no_text_in_row = current_row_element.text # Get text once for efficiency
                            reg_no_match = re.search(r"VA\d{10}", reg_no_text_in_row) # Example, adjust if needed
                            if df_existing is not None and reg_no_match:
                                reg_no = reg_no_match.group(0)
                                if 'registration_number' in df_existing.columns and reg_no in df_existing["registration_number"].values:
                                    log_message(f"Registration {reg_no} (row {row_index}) already in df_existing. Skipping.", level="DEBUG")
                                    row_processed_successfully = True # Mark as processed to advance row_index
                                    break # from row_retries loop, effectively skipping this row

                            # Scroll the link into view before clicking.
                            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", a_link)
                            time.sleep(0.2) # Brief pause after scroll

                            # Use ActionChains to move to the element before clicking
                            actions = ActionChains(driver)
                            actions.move_to_element(a_link).perform()
                            time.sleep(0.3)
                            actions.key_down(Keys.CONTROL).click(a_link).key_up(Keys.CONTROL).perform()
                            time.sleep(1.5) # Increased wait for new tab

                            if len(driver.window_handles) < 2:
                                log_message("New tab did not open as expected after CTRL+CLICK. Retrying row.", level="WARNING")
                                raise Exception("New tab did not open after CTRL+CLICK") # Will be caught by general error handler

                            driver.switch_to.window(driver.window_handles[-1])
                            close_feedback_modal(driver) # On detail page

                            # Parse record details and map the fields.
                            details = parse_record_details(driver)
                            if isinstance(details, str): # "Wrong URL" or "Wrong Tab"
                                log_message(f"parse_record_details returned: '{details}' for row {row_index}. Closing tab.", level="WARNING")
                                if driver.current_url.startswith(USCO_BASE_URL + "/detailed-record"):
                                    driver.close()
                                driver.switch_to.window(driver.window_handles[0])
                                if details == "Wrong URL": # If it's a bad URL, skip this row
                                    row_processed_successfully = True # Mark as "processed" to skip
                                    break # from row_retries
                                raise Exception(f"parse_record_details indicated an issue: {details}") # Retry for "Wrong Tab"

                            record = await format_record(details)                            
                            records.append(record)

                            # Close detail tab and switch back to main search results.
                            driver.close()
                            driver.switch_to.window(driver.window_handles[0])
                            row_processed_successfully = True
                        except StaleElementReferenceException:
                            row_retries += 1
                            log_message(f"StaleElementReferenceException processing row {row_index}, page {page_number}, attempt {row_retries}/3.", level="WARNING")
                            if row_retries < 3:
                                try:
                                    if len(driver.window_handles) > 1 and driver.current_url.startswith(USCO_BASE_URL + "/detailed-record"):
                                        driver.close() # Close detail tab if open
                                    driver.switch_to.window(driver.window_handles[0])
                                    WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "table-list-of-search-results")))
                                    rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex')]")
                                    log_message(f"Re-fetched {len(rows)} rows after StaleElement.", level="DEBUG")
                                    time.sleep(1)
                                except Exception as e_refetch:
                                    log_message(f"Error re-fetching rows after StaleElement: {e_refetch}", level="ERROR")
                                    break # from row_retries
                            else:
                                log_message(f"Failed row {row_index} after 3 StaleElement attempts. Skipping.", level="ERROR")
                                break # from row_retries
                        except (TimeoutException, NoSuchElementException) as e_find:
                            row_retries += 1
                            log_message(f"Find Element Error (Timeout/NoSuchElement) on row {row_index}, attempt {row_retries}/3: {type(e_find).__name__}", level="WARNING")
                            if row_retries < 3:
                                try:
                                    if len(driver.window_handles) > 1 and driver.current_url.startswith(USCO_BASE_URL + "/detailed-record"):
                                        driver.close()
                                    driver.switch_to.window(driver.window_handles[0])
                                    driver.refresh() # Refresh the whole page
                                    WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "table-list-of-search-results")))
                                    rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex')]")
                                    log_message(f"Page refreshed, re-fetched {len(rows)} rows after Find Element Error.", level="DEBUG")
                                    time.sleep(1)
                                except Exception as e_refresh_page:
                                    log_message(f"Error refreshing page/re-fetching rows: {e_refresh_page}", level="ERROR")
                                    break # from row_retries
                            else:
                                log_message(f"Failed row {row_index} after 3 Find Element attempts. Skipping.", level="ERROR")
                                break # from row_retries
                        except Exception as e_general: # Catch other general exceptions for the row
                            row_retries += 1
                            log_message(f"General error on row {row_index}, page {page_number}, attempt {row_retries}/3: {e_general}", level="WARNING")
                            traceback.print_exc()
                            if row_retries < 3:
                                try: # Attempt recovery
                                    main_handle = driver.window_handles[0]
                                    for handle_idx_rec in range(len(driver.window_handles) -1, 0, -1):
                                        driver.switch_to.window(driver.window_handles[handle_idx_rec])
                                        if driver.current_url.startswith(USCO_BASE_URL + "/detailed-record"):
                                            driver.close()
                                    driver.switch_to.window(main_handle)
                                    WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "table-list-of-search-results")))
                                    rows = driver.find_elements(By.XPATH, "//table[@id='table-list-of-search-results']/tbody/div[contains(@class, 'd-flex')]")
                                    log_message(f"Recovered, re-fetched {len(rows)} rows after general error.", level="DEBUG")
                                    time.sleep(1)
                                except Exception as e_recover:
                                    log_message(f"Recovery attempt failed: {e_recover}", level="ERROR")
                                    break # from row_retries
                            else:
                                log_message(f"Failed row {row_index} after 3 general error attempts. Skipping.", level="ERROR")
                                break # from row_retries

                    # Move to the next row only if the current one was processed or skipped
                    row_index += 1

                    if max_results is not None and len(records) >= max_results:
                        print(f"Reached max_results ({max_results}) during row processing. Stopping scrape.")
                        last_page = True # To break outer page loop
                        break # Break from rows loop
                
                if not row_processed_successfully and row_retries == 3 and row_index <= len(rows) : # If the last row attempt failed for all retries
                     log_message(f"Row {row_index-1} (0-indexed) could not be processed after all retries. Will move to next page if available.", level="WARNING")
                
                break # Exit the page retry loop
            except Exception as e: # Catch general exceptions for page processing
                print(f"❌❌ Error processing page {page_number} (Attempt {page_retries + 1} of 3): {e}")
                page_retries += 1
                if page_retries == 3:
                    print(f"❌❌❌ Failed to scrape page {page_number} after 3 retries. Stopping further page attempts for this query.")
                    last_page = True # Critical: ensure we stop if a page consistently fails
                    break # Exit the page retry loop
                # Consider driver health check here if errors persist, though it's also at loop start
                time.sleep(2) # Wait before retrying the page

        if not last_page:
            page_number += 1
        else:
            break  # Exit the page loop if it's the last page

    df_new = pd.DataFrame(records)

    if df_existing is not None:
        df_new = pd.concat([df_existing, df_new])

    if driver_created_internally and driver is not None:
        log_message("Quitting internally created driver in scrape_USCO.", level="INFO")
        driver.quit()
    return df_new if not df_new.empty else None



async def extract_formated_copyright_registration_number(text, source_certificate_path=None):
    # Get the pattern in text: VA d-ddd-ddd
    pattern = r"[TVSP]\s*[XARE]u?\s*\d[\s\-\.\,]\s*\d{3}\s*[\s\-\.\,]\s*\d{3}"  # VA 2-458-745   VA 2.458.745  VA 2458 745  or TX (literary), PA (performing art), SR (sound), SE (serials, e.g. newspapers) and possible u (meaning unbublished)
    match = re.search(pattern, text)

    if not match:
        pattern = r"[TVSP]\s*[XARE]u?\s*\d*"  # VA00002458745
        match = re.search(pattern, text)

    if not match:
        pattern = r"[Vv].*(\d[\s\-\.]\s*\d{3}\s*[\s\-\.]\s*\d{3})"   # Vv A 2-458-745
        match = re.search(pattern, text)
    if not match:
        pattern = r"[Vv].*(\d[\s\-\.\,]*\s*\d\s*\d\s*\d\s*[\s\-\.\,]*\s*\d\s*\d\s*\d)"   # V A 23 3 5-303
        match = re.search(pattern, text)
    if not match:
        pattern = r"[Vv].*(\d[\s\-\.\,]*\s*\d\s*\d\s*\d\s*[\s\-\.\,]*\s*\d\s*\d\s*\d)"   # V A 23 3 5-303
        match = re.search(pattern, text)


    if match:
        raw_match_text = match.group(0)

        # Extract all digits to form the number part
        all_digits = "".join(filter(str.isdigit, raw_match_text))
        if not all_digits:
            return None # No digits found in the match

        # Determine the prefix part:
        # Iterate through raw_match_text to find the first digit.
        # Everything before it constitutes the potential prefix material.
        first_digit_index_in_raw = -1
        for i, char in enumerate(raw_match_text):
            if char.isdigit():
                first_digit_index_in_raw = i
                break

        # Extract prefix characters (letters only) from the part before the first digit
        prefix_material = raw_match_text[:first_digit_index_in_raw]
        extracted_prefix = "".join(filter(str.isalpha, prefix_material)).upper()
        if len(extracted_prefix) == 3 and extracted_prefix[2] == "U":
            extracted_prefix = extracted_prefix[:2] + "u"

        if not extracted_prefix: # No alphabetic prefix found (e.g. match was "12345")
            return None

        # Format the numeric part to be exactly 9/10 digits
        if len(extracted_prefix) == 3:
            formatted_numeric_part = all_digits.zfill(9)
        else:
            formatted_numeric_part = all_digits.zfill(10)

        formatted_reg_no = extracted_prefix + formatted_numeric_part
        print(f"Registration number found: {formatted_reg_no}")
        return formatted_reg_no
    elif source_certificate_path:
        print(f"\033[91mCopyright registration text not found: asking LLM \033[0m")
        prompt = 'Is this a copyright registration certificate? If so, what is the registration number? The registration number always starts with VA or PA or VAu (or similar) and 3 groups of digits separated by dashes. You answer following the format: {"is_copyright_registration": "yes", "registration_number": "VA d-ddd-ddd"} or {"is_copyright_registration": "no"}'

        ai_answer = await vertex_genai_multi_async([("text", prompt), ("image_path", source_certificate_path)])
        ai_answer = get_json(ai_answer)
        if ai_answer and ai_answer["is_copyright_registration"] == "yes":
            print(f"LLM Registration number: {ai_answer['registration_number']}")
            return ai_answer["registration_number"]
        else:
            print(f"\033[91mCopyright registration text not found by asking LLM. This is not a registration certificate. \033[0m")
            return None
    else:
        return None




if __name__ == "__main__":
    # result = get_info_from_USCO_using_reg_no("VA0002309397")
    # print(result)

    # Example usage for testing scrape_USCO for scraping date range
    # df_already_scraped = pd.read_csv("copyright_records.csv")
    # df = asyncio.run(scrape_USCO(name="2-D Artwork", start_date="2024-05-01 00:00:00", end_date="2024-05-03 00:00:00", df_existing=df_already_scraped))
    # df_new = pd.concat([df_already_scraped, df])
    # df_new.to_csv("copyright_records.csv", index=False)

    # Example usage for testing scrape_USCO for scraping by name
    df = asyncio.run(scrape_USCO(name="Stanislav Yurievich Osipov", column_name="claimants", type_of_query="exact", df_existing=None))
    print(df)
    pass
