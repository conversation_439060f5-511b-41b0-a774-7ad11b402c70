"""
Database utilities for the API.
"""

import psycopg2
import psycopg2.extras
import pandas as pd
import json
import os
from datetime import datetime, timedelta
# Module-level cache for API keys
_api_keys_cache = None
_cache_timestamp = None
_cache_expiry_hours = 24

def get_db_connection():
    """
    Get a connection to the PostgreSQL database.
    
    Returns:
        A connection to the PostgreSQL database.
    """
    conn = psycopg2.connect(
        host=os.getenv("POSTGRES_HOST"),
        port=os.getenv("POSTGRES_PORT"),
        user=os.getenv("POSTGRES_USER"),
        password=os.getenv("POSTGRES_PASSWORD"),
        dbname=os.getenv("POSTGRES_DB")
    )
    conn.autocommit = True
    return conn


def get_ip_asset_metadata(asset_ids, ip_types):
    """
    Get metadata for IP assets from PostgreSQL.
    
    Args:
        asset_ids: A list of IP asset IDs.
        
    Returns:
        A dictionary mapping IP asset IDs to their metadata.
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
    
    # Convert asset_ids to a list of UUIDs
    uuids = list(asset_ids)
    metadata = {}
    
    # Query trademarks
    if "Trademark" in ip_types:
        cursor.execute("SELECT * FROM trademarks WHERE id = ANY(%s::uuid[])",(uuids,))
        trademarks = {str(row['id']): dict(row) for row in cursor.fetchall()}
        metadata.update({id: {"type": "trademark", "data": data} for id, data in trademarks.items()})
    
    # Query patents
    if "Patent" in ip_types:
        cursor.execute("SELECT * FROM patents WHERE id = ANY(%s::uuid[])",(uuids,))
        patents = {str(row['id']): dict(row) for row in cursor.fetchall()}
        metadata.update({id: {"type": "patent", "data": data} for id, data in patents.items()})
    
    # Query copyrights
    if "Copyright" in ip_types:
        cursor.execute("SELECT * FROM copyrights WHERE id = ANY(%s::uuid[])",(uuids,))
        copyrights = {str(row['id']): dict(row) for row in cursor.fetchall()}
        metadata.update({id: {"type": "copyright", "data": data} for id, data in copyrights.items()})
    
    # Check for missing assets
    found_ids = set(metadata.keys())
    requested_ids = set(asset_ids)
    missing_ids = requested_ids - found_ids
    for missing_id in missing_ids:
        print(f"⚠️  Error: Asset ID {missing_id} not found in the database.")

    # Close connection
    cursor.close()
    conn.close()
    
    return metadata


def get_reverse_check_results_by_client_id_and_date(client_id, start_date, end_date=None):
    """
    Get all records from the reverse_check_result table for a given client_id and date/date range.
    Args:
        client_id: The client identifier to filter by.
        start_date: The start date to filter by (string, e.g., '2024-06-15').
        end_date: The end date to filter by (string, e.g., '2024-06-20'). If None, only start_date is used.
    Returns:
        A list of row dicts from reverse_check_result.
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

    if end_date is None:
        # Single date query
        cursor.execute("SELECT * FROM reverse_check_result WHERE client_id = %s::text AND DATE(create_time) = %s ORDER BY create_time DESC", (client_id, start_date))
    else:
        # Date range query
        cursor.execute(
            "SELECT * FROM reverse_check_result WHERE client_id = %s::text AND DATE(create_time) BETWEEN %s AND %s ORDER BY create_time DESC",
            (client_id, start_date, end_date)
        )

    results = [dict(row) for row in cursor.fetchall()]
    cursor.close()
    conn.close()
    return results


def create_api_keys_table():
    """
    Create the check_client_api_keys table if it doesn't exist.
    """
    conn = get_db_connection()
    cursor = conn.cursor()

    create_table_sql = """
    CREATE TABLE IF NOT EXISTS check_client_api_keys (
        api_key VARCHAR(255) PRIMARY KEY,
        client_id INTEGER NOT NULL,
        client_name VARCHAR(255) NOT NULL,
        rate_limit INTEGER NOT NULL DEFAULT 1,
        daily_limit INTEGER NOT NULL DEFAULT 100,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE INDEX IF NOT EXISTS idx_client_api_keys_client_id ON check_client_api_keys(client_id);
    """

    cursor.execute(create_table_sql)
    cursor.close()
    conn.close()


def migrate_api_keys_from_json(json_file_path):
    """
    Migrate API keys from JSON file to database.

    Args:
        json_file_path: Path to the api_keys.json file
    """
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Read JSON file
        with open(json_file_path, 'r') as f:
            api_keys_data = json.load(f)

        # Insert each API key
        insert_sql = """
        INSERT INTO check_client_api_keys (api_key, client_id, client_name, rate_limit, daily_limit)
        VALUES (%s, %s, %s, %s, %s)
        ON CONFLICT (api_key) DO UPDATE SET
            client_id = EXCLUDED.client_id,
            client_name = EXCLUDED.client_name,
            rate_limit = EXCLUDED.rate_limit,
            daily_limit = EXCLUDED.daily_limit,
            updated_at = CURRENT_TIMESTAMP
        """

        for api_key, config in api_keys_data.items():
            cursor.execute(insert_sql, (
                api_key,
                config['id'],
                config['client_name'],
                config['rate_limit'],
                config['daily_limit']
            ))

        print(f"Successfully migrated {len(api_keys_data)} API keys to database")

    except Exception as e:
        print(f"Error migrating API keys: {str(e)}")
        raise
    finally:
        cursor.close()
        conn.close()


def get_all_api_keys():
    """
    Retrieve all API keys from database as a pandas DataFrame.

    Returns:
        pandas.DataFrame: DataFrame with API key data
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

    cursor.execute("SELECT * FROM check_client_api_keys ORDER BY client_id")
    rows = cursor.fetchall()

    cursor.close()
    conn.close()

    # Convert to DataFrame
    if rows:
        df = pd.DataFrame([dict(row) for row in rows])
        return df
    else:
        return pd.DataFrame()


def get_api_keys_as_dict():
    """
    Retrieve all API keys from database in the same format as the original JSON.

    Returns:
        dict: Dictionary with API keys as keys and config as values
    """
    conn = get_db_connection()
    cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

    cursor.execute("SELECT api_key, client_id, client_name, rate_limit, daily_limit FROM check_client_api_keys")
    rows = cursor.fetchall()

    cursor.close()
    conn.close()

    # Convert to dictionary format matching original JSON structure
    api_keys_dict = {}
    for row in rows:
        api_keys_dict[row['api_key']] = {
            'id': row['client_id'],
            'client_name': row['client_name'],
            'rate_limit': row['rate_limit'],
            'daily_limit': row['daily_limit']
        }

    return api_keys_dict


def _is_cache_valid():
    """
    Check if the current cache is valid (exists and not expired).

    Returns:
        bool: True if cache is valid, False otherwise
    """
    global _api_keys_cache, _cache_timestamp

    if _api_keys_cache is None or _cache_timestamp is None:
        return False

    # Check if cache has expired (24 hours)
    cache_age = datetime.now() - _cache_timestamp
    return cache_age < timedelta(hours=_cache_expiry_hours)


def _update_cache(api_keys_dict):
    """
    Update the module-level cache with new API keys data.

    Args:
        api_keys_dict: Dictionary of API keys to cache
    """
    global _api_keys_cache, _cache_timestamp

    _api_keys_cache = api_keys_dict.copy()
    _cache_timestamp = datetime.now()


def get_cached_api_keys():
    """
    Get API keys with caching mechanism. Uses cache if valid, otherwise fetches from database.

    Returns:
        dict: Dictionary with API keys as keys and config as values
    """
    global _api_keys_cache

    # Check if cache is valid
    if _is_cache_valid():
        return _api_keys_cache.copy()

    # Cache is invalid, fetch from database
    try:
        api_keys_dict = get_api_keys_as_dict()
        _update_cache(api_keys_dict)
        return api_keys_dict
    except Exception as e:
        # If database fetch fails and we have stale cache, use it
        if _api_keys_cache is not None:
            print(f"Warning: Database fetch failed, using stale cache: {str(e)}")
            return _api_keys_cache.copy()
        else:
            # No cache and database failed, re-raise the exception
            raise


def refresh_api_keys_cache():
    """
    Force refresh the API keys cache from database.

    Returns:
        dict: Fresh API keys data from database
    """
    api_keys_dict = get_api_keys_as_dict()
    _update_cache(api_keys_dict)
    return api_keys_dict
