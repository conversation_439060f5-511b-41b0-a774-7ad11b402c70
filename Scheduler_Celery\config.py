import os
from datetime import timedelta
from celery.schedules import crontab

class CeleryConfig:
    # Celery settings
    broker_url = os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/0')
    result_backend = os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
    
    # Timezone settings
    timezone = 'America/New_York'
    enable_utc = True
    
    # Task serialization
    task_serializer = 'json'
    accept_content = ['json']
    result_serializer = 'json'
    result_expires = 604800 # 7 days
    
    # Worker settings
    worker_prefetch_multiplier = 1
    task_acks_late = True
    
    # Beat scheduler
    beat_scheduler = 'celery.beat:PersistentScheduler'
    
    # Beat schedule for periodic tasks
    beat_schedule = {
        'daily-case-fetch': {
            'task': 'Scheduler_Celery.tasks.daily_case_fetch_task',
            'schedule': crontab(hour=0, minute=30),  # 00:30 every day NY time
            'options': {'timezone': 'America/New_York'}
        },
    }