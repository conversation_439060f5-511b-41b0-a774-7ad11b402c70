from .celery_app import celery_app
from .utils.email_utils import send_daily_report_email, send_error_notification_email
import logging
from datetime import datetime
import traceback
import pandas as pd

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@celery_app.task(bind=True, name='Scheduler_Celery.tasks.daily_case_fetch_task')
def daily_case_fetch_task(self, num_days=1):
    """
    Daily task to fetch new cases and send email report
    Runs every day at 00:30 NY time
    """
    try:
        logger.info(f"Starting daily case fetch task with {num_days} days")
        self.update_state(state='PROGRESS', meta={'status': 'Starting case fetch...'})

        from Alerts.WorkflowManager import fetch
        self.update_state(state='PROGRESS', meta={'status': 'Fetching new cases...'})
        new_cases_df, tracking_dict = fetch(num_days)
        stats = get_case_statistics(new_cases_df, tracking_dict)
        self.update_state(state='PROGRESS', meta={'status': 'Sending email report...'})
        send_daily_report_email(stats)
        
        logger.info("Daily case fetch task completed successfully")
        
        return {
            'status': 'completed',
            'timestamp': datetime.now().isoformat(),
            'stats': stats,
            'message': 'Daily case fetch completed successfully'
        }
        
    except Exception as exc:
        logger.error(f"Daily case fetch task failed: {str(exc)}")
        logger.error(traceback.format_exc())
        
        send_error_notification_email(str(exc), "Daily Case Fetch")
        self.update_state(
            state='FAILURE',
            meta={
                'error': str(exc),
                'traceback': traceback.format_exc()
            }
        )
        raise exc

def get_case_statistics(new_cases_df, tracking_dict):
    """
    Extract statistics from the fetch result and IP tracking data
    """
    try:
        total_new_cases = len(new_cases_df) if isinstance(new_cases_df, pd.DataFrame) else 0

        # Handle empty DataFrame case
        if total_new_cases == 0:
            logger.info("No new cases found - returning empty statistics")
            return {
                'total_new_cases': 0,
                'ip_statistics': {
                    'cases_no_ip_gained_some': 0,
                    'cases_no_ip_gained_all': 0,
                    'cases_some_ip_gained_more': 0,
                    'cases_some_ip_gained_all': 0,
                    'cases_ip_regressed': 0,
                    'cases_already_complete': 0,
                    'total_processed': 0
                },
                'fetch_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'case_details': [],
                'no_cases_found': True
            }
        
        ip_stats = {
            'cases_no_ip_gained_some': tracking_dict.get('cases_no_ip_then_some_ip', 0),
            'cases_no_ip_gained_all': tracking_dict.get('cases_no_ip_then_all_ip', 0),
            'cases_some_ip_gained_more': tracking_dict.get('cases_some_ip_then_more_ip', 0),
            'cases_some_ip_gained_all': tracking_dict.get('cases_some_ip_then_all_ip', 0),
            'cases_ip_regressed': tracking_dict.get('cases_ip_regressed', 0),
            'cases_already_complete': tracking_dict.get('cases_already_all_ip', 0),
            'total_processed': tracking_dict.get('total_count', 0)
        }

        return {
            'total_new_cases': total_new_cases,
            'ip_statistics': ip_stats,
            'fetch_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'case_details': new_cases_df.to_dict('records') if isinstance(new_cases_df, pd.DataFrame) and not new_cases_df.empty else [],
            'no_cases_found': False
        }
        
    except Exception as e:
        logger.error(f"Error getting case statistics: {str(e)}")
        return {
            'total_new_cases': 0,
            'ip_statistics': {
                'cases_no_ip_gained_some': 0,
                'cases_no_ip_gained_all': 0,
                'cases_some_ip_gained_more': 0,
                'cases_some_ip_gained_all': 0,
                'cases_ip_regressed': 0,
                'cases_already_complete': 0,
                'total_processed': 0
            },
            'fetch_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'case_details': [],
            'error': str(e),
            'no_cases_found': True
        }

@celery_app.task
def test_task():
    """Simple test task to verify Celery is working"""
    logger.info("Test task executed successfully")
    return "Test task completed successfully!"

# Task monitoring signals
from celery.signals import task_prerun, task_postrun, task_failure

@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    logger.info(f'Task {task.name} started: {task_id}')

@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, retval=None, state=None, **kwds):
    logger.info(f'Task {task.name} completed: {task_id} - State: {state}')

@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwds):
    logger.error(f'Task {sender.name} failed: {task_id} - Exception: {exception}')