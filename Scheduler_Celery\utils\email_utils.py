import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
import logging
import os
from langfuse import observe

logger = logging.getLogger(__name__)

@observe(name="Send Daily Report Email", capture_input=False, capture_output=False)
def send_daily_report_email(stats):
    try:   
        # Create message
        msg = MIMEMultipart()
        msg['From'] = '<EMAIL>'
        msg['To'] = '<EMAIL>, <EMAIL>'
        msg['Subject'] = f"Daily Case Fetch Report - {stats['fetch_date']}"
        
        # Check if no cases were found
        if stats.get('no_cases_found', False):
            body = f"""
                    Daily Case Fetch Report 📊
                    ==========================

                    Date: {stats['fetch_date']}

                    📭 No New Cases Found Today

                    The system completed its daily scan but didn't find any new cases in the specified date range. This is normal and could be due to:
                    • No new cases filed today
                    • Weekend or holiday period
                    • Courts not in session

                    The system will continue monitoring and will process any new cases in tomorrow's run.

                    ---
                    Automated report from Case Management System
                """
        else:
            # Get IP statistics
            ip_stats = stats['ip_statistics']
            
            # Helper function to safely get integer values
            def safe_int(value):
                if isinstance(value, (list, tuple)):
                    return len(value) if value else 0
                return int(value) if value is not None else 0
            
            # Extract stats safely
            cases_gained_some = safe_int(ip_stats.get('cases_no_ip_gained_some', 0))
            cases_gained_all = safe_int(ip_stats.get('cases_no_ip_gained_all', 0))
            cases_improved = safe_int(ip_stats.get('cases_some_ip_gained_more', 0))
            cases_completed = safe_int(ip_stats.get('cases_some_ip_gained_all', 0))
            cases_regressed = safe_int(ip_stats.get('cases_ip_regressed', 0))
            cases_already_complete = safe_int(ip_stats.get('cases_already_complete', 0))
            total_processed = safe_int(ip_stats.get('total_processed', 0))
            
            # Calculate progress metrics
            cases_with_progress = cases_gained_some + cases_gained_all + cases_improved + cases_completed
            
            body = f"""
                    Daily Case Fetch Report 📊
                    ==========================

                    Date: {stats['fetch_date']}

                    ✅ Successfully processed {stats['total_new_cases']} new cases

                    🎯 Progress Summary:
                    • {cases_with_progress} cases made progress in IP collection
                    • {cases_already_complete} cases were already complete
                    • {total_processed} total cases processed

                    📈 Detailed Results:

                    New Cases That Gained IP:
                    • Started with no IP, now have some: {cases_gained_some}
                    • Started with no IP, now complete: {cases_gained_all}

                    Existing Cases That Improved:
                    • Had partial IP, gained more: {cases_improved}
                    • Had partial IP, now complete: {cases_completed}

                    Other Updates:
                    • Cases that lost IP: {cases_regressed}
                    • Cases already complete: {cases_already_complete}

                    🎉 Great job! The daily workflow completed successfully and all new cases have been processed through the IP analysis pipeline.

                    ---
                    Automated report from Case Management System
                """
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email via SMTP server
        with smtplib.SMTP('smtp.email.us-ashburn-1.oci.oraclecloud.com', 587) as server:
            server.starttls()
            server.login(os.getenv('OCI_EMAIL_USERNAME'), os.getenv('OCI_EMAIL_PASSWORD'))
            server.send_message(msg)

        
        logger.info("Daily report email sent successfully")
        
    except Exception as e:
        logger.error(f"Failed to send daily report email: {str(e)}")

@observe(name="Send Error Notification Email", capture_input=False, capture_output=False)
def send_error_notification_email(error_msg, task_name="Daily Case Fetch"):
    try:
        # Create message
        msg = MIMEMultipart()
        msg['From'] = '<EMAIL>'
        msg['To'] = '<EMAIL>;<EMAIL>'
        msg['Subject'] = f"🚨 {task_name} - FAILED"
        
        # Email body
        body = f"""
                🚨 {task_name} Error Alert
                {'=' * (len(task_name) + 12)}

                The {task_name.lower()} task has failed with the following error:

                Error Details:
                --------------
                {error_msg}

                Action Required:
                ---------------
                Please check the application logs for more details and investigate the issue.

                Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

                ---
                Automated error notification from Case Management System
            """
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email via SMTP server
        with smtplib.SMTP('smtp.email.us-ashburn-1.oci.oraclecloud.com', 587) as server:
            server.starttls()
            server.login(os.getenv('OCI_EMAIL_USERNAME'), os.getenv('OCI_EMAIL_PASSWORD'))
            server.send_message(msg)
        
        logger.info("Error notification email sent")
        
    except Exception as e:
        logger.error(f"Failed to send error notification email: {str(e)}")