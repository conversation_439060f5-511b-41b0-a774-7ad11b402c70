// Check form submission logic

// Initialize check form functionality
function initializeCheckForm() {
    const checkForm = document.getElementById("checkForm");
    const uploadActiveInput = document.getElementById("main_product_image_upload_active");
    const urlInput = document.getElementById("main_product_image_url");
    const fileInput = document.getElementById("main_product_image_upload");
    
    if (!checkForm) return;

    // Prevent submitting both file and URL for main product image
    checkForm.addEventListener("submit", function(e) {
        if (uploadActiveInput.value === "true" && urlInput.value.trim() !== "") {
            alert("Please either upload a file OR provide a URL, not both.");
            e.preventDefault();
            return;
        }
        if (uploadActiveInput.value === "false") {
            fileInput.value = "";
        }
    });

    // Additional validation for form submission
    checkForm.addEventListener("submit", function(e) {
        const otherUploadActiveInput = document.getElementById("other_product_images_upload_active");
        const otherUrlInput = document.getElementById("other_product_images_url");
        const ipUploadActiveInput = document.getElementById("ip_images_upload_active");
        const ipUrlInput = document.getElementById("ip_images_url");
        const otherFilesInput = document.getElementById("other_product_images_upload");
        const ipFilesInput = document.getElementById("ip_images_upload");

        const otherUploadActive = otherUploadActiveInput.value;
        const otherUrl = otherUrlInput.value.trim();
        if(otherUploadActive === "true" && otherUrl !== "") {
            alert("Please either upload Other Product Images OR provide URLs, not both.");
            e.preventDefault();
            return;
        }
        const ipUploadActive = ipUploadActiveInput.value;
        const ipUrl = ipUrlInput.value.trim();
        if(ipUploadActive === "true" && ipUrl !== "") {
            alert("Please either upload IP Images OR provide URLs, not both.");
            e.preventDefault();
            return;
        }
        if(otherUploadActive === "false") {
            otherFilesInput.value = "";
        }
        if(ipUploadActive === "false") {
            ipFilesInput.value = "";
        }
    });

    // Main submission logic with spinner and temporary message
    checkForm.addEventListener("submit", function(e) {
        e.preventDefault();
        
        // Disable the submit button to prevent duplicate submissions
        const submitButton = this.querySelector("button[type='submit']");
        submitButton.disabled = true;
        
        // Display a spinner and processing message
        const outputDiv = document.getElementById("output");
        outputDiv.innerHTML = `
            <div class="info-message">
                <div style='display:flex; align-items:center; gap:10px;'>
                    <div class='spinner'></div>
                    <span data-i18n='processing'></span>
                </div>
            </div>`;
        
        // Trigger language update immediately after creating the message
        document.querySelector('[data-i18n="processing"]').textContent = i18n[document.getElementById("language").value].processing;

        // Immediate scroll to top with header offset
        const header = document.querySelector('.header');
        const headerHeight = header ? header.offsetHeight : 0;
        
        // New approach: Use documentElement scroll with CSS scroll-padding
        document.documentElement.style.scrollPaddingTop = `${headerHeight}px`;
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });

        // Gather form values
        let formData;
        try {
            formData = gatherFormData();
        } catch (error) {
            handleError(error, outputDiv, submitButton);
            return;
        }

        // Add timeout controller
        const controller = new AbortController();
        const timeoutDuration = 600000; // 10 minutes
        const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

        // Make the API call
        // const api_endpoint = "http://localhost:5000/check_api";
        // const api_endpoint = "http://vectorstore1.maidalv.com:5090/check_api";
        // const api_endpoint = "https://api.maidalv.com/check_api";
        const api_endpoint = "/check_api";
        
        submitCheckRequest(api_endpoint, formData, controller, timeoutId, submitButton, outputDiv);
    });
}

// Gather form data
function gatherFormData() {
    const uploadActiveInput = document.getElementById("main_product_image_upload_active");
    const previewImage = document.getElementById("main_product_image_preview");
    const urlInput = document.getElementById("main_product_image_url");
    
    const api_key = document.getElementById("api_key").value;
    const mainProductImageValue = (uploadActiveInput.value === "true") ? previewImage.src : urlInput.value.trim();
    
    // For other product images
    let other_product_images;
    if (document.getElementById("other_product_images_upload_active").value === "true") {
        const previewImages = document.querySelectorAll("#other_product_images_previews img");
        if (previewImages.length === 0) {
            throw new Error("Other Product Images are set to upload, but no files were provided.");
        }
        other_product_images = Array.from(previewImages).map(img => img.src);
    } else {
        other_product_images = document.getElementById("other_product_images_url").value.split(",").map(item => item.trim()).filter(item => item);
    }

    // For IP images, read directly from preview images if uploaded
    const ipUploadActiveInput = document.getElementById("ip_images_upload_active");
    let ip_images;
    if (ipUploadActiveInput.value === "true") {
        const previewImages = document.querySelectorAll("#ip_images_previews img");
        if (previewImages.length === 0) {
            throw new Error("IP Images are set to upload, but no files were provided.");
        }
        ip_images = Array.from(previewImages).map(img => img.src);
    } else {
        ip_images = document.getElementById("ip_images_url").value.split(",").map(item => item.trim()).filter(item => item);
    }

    const ip_keywords = document.getElementById("ip_keywords").value.split(",").map(item => item.trim()).filter(item => item);
    const description = document.getElementById("description").value;
    const reference_text = document.getElementById("reference_text").value;

    // For Reference images, read directly from preview images if uploaded
    let reference_images;
    if (document.getElementById("reference_images_upload_active").value === "true") {
        const previewImages = document.querySelectorAll("#reference_images_previews img");
        if (previewImages.length === 0) {
            throw new Error("Reference Images are set to upload, but no files were provided.");
        }
        reference_images = Array.from(previewImages).map(img => img.src);
    } else {
        reference_images = document.getElementById("reference_images").value.split(",").map(item => item.trim()).filter(item => item);
    }
    
    return {
        api_key: api_key,
        main_product_image: mainProductImageValue,
        other_product_images: other_product_images,
        ip_images: ip_images,
        ip_keywords: ip_keywords,
        description: description,
        reference_text: reference_text,
        reference_images: reference_images,
        language: document.getElementById("language").value
    };
}

// Submit check request
function submitCheckRequest(api_endpoint, data, controller, timeoutId, submitButton, outputDiv) {
    fetch(api_endpoint, {
        method: "POST",
        headers: {"Content-Type": "application/json"},
        body: JSON.stringify(data),
        signal: controller.signal
    })
    .then(response => {
        clearTimeout(timeoutId);
        return handleResponse(response);
    })
    .then(initialResponse => {
        if (!initialResponse.check_id) throw new Error('Missing check ID in response');
        
        // If the response indicates an error, handle it
        if (initialResponse.status === 'failed' || initialResponse.error) {
            const errorMessage = initialResponse.error || 'An unknown error occurred';
            throw new Error(errorMessage);
        }
        
        // Display initial queue position and estimated time if available
        if (initialResponse.status === 'processing') {                        
            let estimatedTimeMessage = initialResponse.estimated_completion_time !== undefined
                ? `${i18n[document.getElementById("language").value].estimated_time}: ${initialResponse.estimated_completion_time}`
                : '';

            outputDiv.innerHTML = `
                <div class="info-message">
                    <div style='display:flex; align-items:center; gap:10px;'>
                        <div class='spinner'></div>
                        <span data-i18n='running'></span>
                        <span>${estimatedTimeMessage}</span>
                    </div>
                </div>`;
            document.querySelector('[data-i18n="running"]').textContent = i18n[document.getElementById("language").value].running;
        }
        
        // Start polling for results
        startPolling(initialResponse.check_id, outputDiv, submitButton);
    })
    .catch(error => {
        clearTimeout(timeoutId);
        handleError(error, outputDiv, submitButton);
    });
}

// Handle response
function handleResponse(response) {
    if (response.ok) {
        if (!response.headers.get("content-type")?.includes("application/json")) {
            return Promise.reject(new Error(`Invalid content type: ${response.headers.get("content-type")}`));
        }
        return response.json();
    }

    // If response is not ok, get text first to avoid JSON parsing errors on non-JSON bodies.
    return response.text().then(errorText => {
        let errorJson;
        try {
            // Try to parse the error text as JSON.
            errorJson = JSON.parse(errorText);
        } catch (e) {
            // If it's not JSON, create a standard error structure.
            const error = new Error(errorText || `HTTP Error ${response.status}: ${response.statusText}`);
            error.error_code = 'NETWORK_ERROR';
            error.details = `The server returned a non-JSON error for a ${response.status} status.`;
            throw error;
        }

        // If it is JSON, extract the details.
        const errorData = errorJson.error || {};
        const message = errorData.message || `Error ${response.status}: ${response.statusText}`;
        const error = new Error(message);
        error.error_code = errorData.error_code;
        error.details = errorData.details || errorText; // Fallback to raw text if details are missing
        throw error;
    });
}

// Start polling for results
function startPolling(checkId, outputDiv, submitButton) {
    const pollInterval = 5000; // 5 seconds
    const maxPolls = 60; // 5 minutes total
    let polls = 0;

    const poller = setInterval(() => {
        if (polls++ >= maxPolls) {
            clearInterval(poller);
            const timeoutError = new Error('Analysis timed out');
            timeoutError.error_code = 'TIMEOUT_ERROR';
            handleError(timeoutError, outputDiv, submitButton);
            return;
        }

        fetch(`/check_status/${checkId}`)
        .then(handleResponse) // Use the centralized, robust response handler
        .then(statusResponse => {
            console.log("Polling response:", statusResponse);

            if (statusResponse.status === 'error') {
                // Handle error status in a successful response
                clearInterval(poller);
                const language = document.getElementById("language").value;
                const errorMessage = statusResponse.message || i18n[language].error_server;
                const errorCode = statusResponse.error_code || '';

                // Create error object for consistent handling
                const error = new Error(errorMessage);
                error.error_code = errorCode;
                error.details = statusResponse.details || errorMessage;

                handleError(error, outputDiv, submitButton);
                return;
            }

            if (statusResponse.status === 'completed') {
                clearInterval(poller);
                displayResults(statusResponse.result);
                submitButton.disabled = false;
            } else if (statusResponse.status === 'processing') {
                // Update UI with processing message
                let estimatedTimeMessage = statusResponse.estimated_completion_time !== undefined
                    ? `${i18n[document.getElementById("language").value].estimated_time} ${statusResponse.estimated_completion_time}`
                    : '';

                outputDiv.innerHTML = `
                    <div class="info-message">
                        <div style='display:flex; align-items:center; gap:10px;'>
                            <div class='spinner'></div>
                            <span data-i18n='processing'></span>
                            <span>${estimatedTimeMessage}</span>
                        </div>
                    </div>`;
                document.querySelector('[data-i18n="processing"]').textContent = i18n[document.getElementById("language").value].processing;
            }
            // Errors from the fetch (like 4xx or 5xx responses) are now handled by the .catch block
        })
        .catch(error => {
            // This catches network errors or errors thrown by handleResponse
            clearInterval(poller);
            handleError(error, outputDiv, submitButton);
        });
    }, pollInterval);
}

// Handle errors
function handleError(error, outputDiv, submitButton) {
    const language = document.getElementById("language").value;
    const i18nKey = error.error_code ? `error_${error.error_code.toLowerCase()}` : '';

    let displayMessage;

    // Use i18n translation if available, otherwise fallback to the error message
    if (i18nKey && i18n[language][i18nKey]) {
        displayMessage = i18n[language][i18nKey];
        // Handle dynamic fields in the message, e.g., for MISSING_REQUIRED_FIELD
        if (error.error_code === 'MISSING_REQUIRED_FIELD' && error.details) {
            displayMessage += error.details;
        }
    } else {
        displayMessage = error.message || i18n[language].error_server; // Fallback to a generic server error
    }

    // Log details for debugging
    if (error.details) {
        console.error("Error Details:", error.details);
    }
    
    // Handle special cases like AbortError for timeouts
    if (error.name === 'AbortError') {
        displayMessage = i18n[language].error_timeout;
        error.error_code = 'TIMEOUT_ERROR';
    } else if (!error.error_code) {
        // Assign a generic code for network or unexpected client-side errors
        error.error_code = 'NETWORK_ERROR';
        displayMessage = i18n[language].error_network;
    }

    // Display the error with better formatting
    outputDiv.innerHTML = `
        <div class="error-message">
            <strong>Error</strong>
            <p>${displayMessage}</p>
            ${error.error_code ? `<div class="error-code">${error.error_code}</div>` : ''}
        </div>
    `;

    // Re-enable submit button even on error
    if (submitButton) {
        submitButton.disabled = false;
    }
}

// Export functions for global access
window.initializeCheckForm = initializeCheckForm;
